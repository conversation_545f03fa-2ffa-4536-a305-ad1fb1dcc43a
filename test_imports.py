#!/usr/bin/env python3
"""Test script to check imports."""

print("Testing imports...")

try:
    import sys
    print("✅ sys imported")
    
    import os
    print("✅ os imported")
    
    import json
    print("✅ json imported")
    
    from pathlib import Path
    print("✅ pathlib imported")
    
    # Test Google imports
    try:
        import google.auth
        print("✅ google.auth imported")
    except ImportError as e:
        print(f"❌ google.auth failed: {e}")
    
    try:
        import google.oauth2
        print("✅ google.oauth2 imported")
    except ImportError as e:
        print(f"❌ google.oauth2 failed: {e}")
    
    try:
        import googleapiclient
        print("✅ googleapiclient imported")
    except ImportError as e:
        print(f"❌ googleapiclient failed: {e}")
    
    # Test our modules
    sys.path.insert(0, 'src')
    
    try:
        from music_sources.base import Track, LoFiStyle
        print("✅ music_sources.base imported")
    except ImportError as e:
        print(f"❌ music_sources.base failed: {e}")
    
    try:
        from youtube.metadata import VideoMetadata
        print("✅ youtube.metadata imported")
    except ImportError as e:
        print(f"❌ youtube.metadata failed: {e}")
    
    try:
        from youtube.uploader import YouTubeUploader
        print("✅ youtube.uploader imported")
    except ImportError as e:
        print(f"❌ youtube.uploader failed: {e}")
    
    print("✅ All tests completed")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
