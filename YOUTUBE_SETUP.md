# YouTube Upload Setup Guide

This guide will help you set up the YouTube upload functionality for your lo-fi video channel.

## 🚀 Quick Setup

### 1. Install Dependencies

The required dependencies are already included in `pyproject.toml`. If you need to install them manually:

```bash
pip install sentry-sdk schedule pillow
```

### 2. YouTube API Setup

Run the interactive setup script:

```bash
python scripts/setup_youtube.py
```

This will guide you through:
- Creating Google Cloud project
- Enabling YouTube Data API v3
- Setting up service account
- Testing the connection

### 3. Environment Configuration

Update your `.env` file with the new variables:

```bash
# YouTube Upload Configuration
YOUTUBE_SERVICE_ACCOUNT_FILE=path/to/service-account-key.json
YOUTUBE_CHANNEL_ID=your_youtube_channel_id_here

# Scheduling Configuration
TIMEZONE=UTC
DEFAULT_PUBLISH_TIME=22:00

# Slack Notifications (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# GitLab Error Tracking (optional)
SENTRY_DSN=https://<EMAIL>/api/v4/error_tracking/collector/your-project-id

# Database Configuration
DATABASE_PATH=lofi_channel.db
```

### 4. Test the Setup

Run the example script to test everything:

```bash
python examples/upload_example.py
```

## 📋 New CLI Commands

### Upload a Video

```bash
# Upload as draft
python lofi_cli.py upload video.mp4 chill

# Upload and schedule for 10 PM today
python lofi_cli.py upload video.mp4 chill -t 22:00

# Upload and schedule for specific date
python lofi_cli.py upload video.mp4 chill -t 22:00 -d 2024-12-25
```

### Manage Videos

```bash
# Check upload status
python lofi_cli.py status

# Publish a video immediately
python lofi_cli.py publish 5  # Database ID from status command
```

### Generate and Upload in One Step

```bash
# Generate a 3-minute chill video and upload it
python lofi_cli.py generate 180 chill -o my_video.mp4
python lofi_cli.py upload my_video.mp4 chill -t 22:00
```

## ⏰ Automatic Publishing

Set up a cron job to automatically publish scheduled videos:

```bash
# Edit your crontab
crontab -e

# Add this line to check every 5 minutes
*/5 * * * * cd /path/to/lofi-channel && python scripts/publish_scheduler.py >> /var/log/lofi-scheduler.log 2>&1
```

## 🔧 Configuration Options

### Video Naming

Videos are automatically named using the pattern:
- `videos/my_lofi_video_00001.mp4` (incremental)
- `videos/my_lofi_video_YYMMDD-HHMM.mp4` (timestamp)

### Content Guidelines

Set up content validation rules in the database:

```python
# Example: Add content guidelines
from src.database.manager import DatabaseManager
from src.database.models import ContentGuideline

db = DatabaseManager()
await db.initialize()

guidelines = [
    ContentGuideline(rule_name="max_title_length", rule_value="100"),
    ContentGuideline(rule_name="require_attribution", rule_value="true"),
    ContentGuideline(rule_name="prohibited_words", rule_value="spam,fake,clickbait")
]

for guideline in guidelines:
    await db.add_content_guideline(guideline)
```

## 📊 Monitoring and Notifications

### Slack Integration

1. Create a Slack webhook URL
2. Set `SLACK_WEBHOOK_URL` in your `.env` file
3. Receive notifications for:
   - Upload started/completed/failed
   - Publishing scheduled/completed
   - Duplicate detection
   - Content violations
   - System errors

### Error Tracking

1. Enable GitLab error tracking in your project
2. Copy the DSN from GitLab settings
3. Set `SENTRY_DSN` in your `.env` file
4. Monitor errors in GitLab's error tracking interface

## 🗄️ Database Management

### View Upload History

```bash
# Check all videos
sqlite3 lofi_channel.db "SELECT id, title, upload_status, youtube_video_id FROM uploaded_videos ORDER BY created_at DESC;"

# Find scheduled videos
sqlite3 lofi_channel.db "SELECT id, title, scheduled_publish_time FROM uploaded_videos WHERE upload_status='scheduled';"

# Check failed uploads
sqlite3 lofi_channel.db "SELECT id, title, created_at FROM uploaded_videos WHERE upload_status='failed';"
```

### Cleanup Old Records

```bash
# Remove old failed uploads (older than 30 days)
sqlite3 lofi_channel.db "DELETE FROM uploaded_videos WHERE upload_status='failed' AND created_at < datetime('now', '-30 days');"
```

## 🔒 Security Best Practices

### Service Account Security

- Store JSON key file outside the project directory
- Set restrictive file permissions: `chmod 600 service-account-key.json`
- Never commit the key file to version control
- Rotate keys periodically

### Environment Variables

- Use `.env` file for local development
- Use secure secret management in production
- Validate all environment variables on startup

## 🐛 Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Check service account file path
   - Verify service account has channel access
   - Ensure YouTube Data API v3 is enabled

2. **"Upload quota exceeded"**
   - Check YouTube API quota in Google Cloud Console
   - Wait for quota reset (daily)
   - Consider uploading during off-peak hours

3. **"Scheduled videos not publishing"**
   - Verify cron job is running: `crontab -l`
   - Check scheduler logs: `tail -f scheduler.log`
   - Verify timezone configuration

### Debug Commands

```bash
# Test YouTube connection
python scripts/setup_youtube.py

# Check upload pipeline status
python lofi_cli.py status

# Test Slack notifications
python -c "
import asyncio
from src.notifications.slack import SlackNotifier
notifier = SlackNotifier('YOUR_WEBHOOK_URL')
asyncio.run(notifier.test_connection())
"

# View recent logs
tail -f lofi_cli.log
tail -f scheduler.log
```

## 📈 Performance Tips

### Upload Optimization

- Use H.264 codec for best compatibility
- Keep video bitrate reasonable (1-5 Mbps)
- Upload during off-peak hours (varies by region)
- Monitor upload success rates

### Database Performance

- Regular VACUUM operations: `sqlite3 lofi_channel.db "VACUUM;"`
- Monitor database size growth
- Archive old records periodically

## 🎯 Next Steps

1. **Test the complete workflow**:
   - Generate a video
   - Upload as draft
   - Schedule for publishing
   - Monitor via Slack/GitLab

2. **Set up monitoring**:
   - Configure Slack webhooks
   - Enable GitLab error tracking
   - Set up log rotation

3. **Automate the pipeline**:
   - Create cron jobs for publishing
   - Consider automated video generation
   - Set up backup procedures

4. **Scale up**:
   - Batch upload operations
   - Implement playlist management
   - Add analytics tracking

For detailed documentation, see `docs/youtube-upload.md`.
