# =============================================================================
# Lo-Fi Video Generator - .gitignore
# =============================================================================

# Python
# -----------------------------------------------------------------------------
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Pixi / Conda
# -----------------------------------------------------------------------------
.pixi/
*.conda
*.tar.bz2

# Project-specific
# -----------------------------------------------------------------------------
# Generated videos (output directory)
videos/
*.mp4
*.webm
*.avi
*.mov
*.mkv
*.gif
*.apng
*.webp
*.mng

# Audio cache and temporary files
lofi_cache/
audio_cache/
temp_audio/
backgrounds/
*.mp3
*.wav
*.ogg
*.flac
*.aac
*.m4a

# FFmpeg temporary files
ffmpeg_*.log
temp_video_*
temp_audio_*

# Application logs
*.log
logs/
lofi_cli.log

# Configuration files with sensitive data
.env.local
.env.production
config.local.json
secrets.json

# API keys and credentials
api_keys.txt
credentials.json
.secrets

# IDE and Editor
# -----------------------------------------------------------------------------
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Operating System
# -----------------------------------------------------------------------------
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Documentation
# -----------------------------------------------------------------------------
# Sphinx build outputs
docs/_build/
docs/_static/generated/
docs/api/_autosummary/

# ReadTheDocs
.readthedocs.yml.bak

# Backup files
# -----------------------------------------------------------------------------
*.bak
*.backup
*.old
*.orig
*.save
*~

# Temporary files
# -----------------------------------------------------------------------------
tmp/
temp/
.tmp/
.temp/

# Database files
# -----------------------------------------------------------------------------
*.db
*.sqlite
*.sqlite3

# Monitoring and Analytics
# -----------------------------------------------------------------------------
# Sentry local config
.sentryclirc

# Performance monitoring
*.prof
*.pstats
