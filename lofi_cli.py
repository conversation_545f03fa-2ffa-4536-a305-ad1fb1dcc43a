#!/usr/bin/env python3
"""
Lo-Fi Video Generator CLI - Legacy wrapper for the modern CLI

This file provides backward compatibility. The new modern CLI is in src/cli/main.py
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point - delegates to modern CLI."""
    try:
        from cli.main import app
        app()
    except ImportError:
        print("❌ Failed to import modern CLI. Please install dependencies:")
        print("pip install typer rich")
        sys.exit(1)


if __name__ == "__main__":
    main()

        self.video_generator = None
        self.upload_pipeline = None

    async def setup(self, enable_upload: bool = False):
        """Initialize managers and check dependencies."""
        try:
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv()

            # Initialize error tracking
            sentry_dsn = os.getenv("SENTRY_DSN")
            if sentry_dsn:
                initialize_error_tracking(
                    dsn=sentry_dsn,
                    environment=os.getenv("ENVIRONMENT", "production"),
                    debug=os.getenv("DEBUG", "false").lower() == "true"
                )

            # Initialize music manager
            self.music_manager = LoFiMusicManager()

            # Configure music sources
            freesound_key = os.getenv("FREESOUND_API_KEY")
            if not freesound_key:
                error_msg = "FREESOUND_API_KEY not found in environment"
                logger.error(error_msg)
                logger.info("Please set your Freesound API key:")
                logger.info("export FREESOUND_API_KEY='your_key_here'")
                logger.info("Get your key at: https://freesound.org/apiv2/apply")
                return False

            self.music_manager.configure_sources(freesound_key)

            # Initialize video generator
            self.video_generator = LoFiVideoGenerator()

            # Check video dependencies
            deps_ok, missing = self.video_generator.check_dependencies()
            if not deps_ok:
                logger.error("❌ Missing dependencies:")
                for dep in missing:
                    logger.error(f"  - {dep}")
                logger.info("\nPlease install FFmpeg:")
                logger.info("  Ubuntu/Debian: sudo apt install ffmpeg")
                logger.info("  macOS: brew install ffmpeg")
                logger.info("  Windows: Download from https://ffmpeg.org/")
                return False

            # Initialize upload pipeline if requested
            if enable_upload:
                service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
                slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
                timezone = os.getenv("TIMEZONE", "UTC")

                if not service_account_file:
                    logger.error("YOUTUBE_SERVICE_ACCOUNT_FILE not found in environment")
                    logger.info("Please set your YouTube service account file path:")
                    logger.info("export YOUTUBE_SERVICE_ACCOUNT_FILE='path/to/service-account.json'")
                    return False

                self.upload_pipeline = UploadPipeline(
                    service_account_file=service_account_file,
                    slack_webhook_url=slack_webhook_url,
                    timezone=timezone
                )

                if not await self.upload_pipeline.initialize():
                    logger.error("Failed to initialize upload pipeline")
                    return False

            return True

        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False

    async def generate_video(self, duration: float, style: str, output: Optional[str] = None, background: Optional[str] = None):
        """Generate a lo-fi video with specified duration and style."""

        # Parse style
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            logger.error(f"Invalid style: {style}")
            logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
            return False

        # Validate background video if provided
        if background:
            background_path = Path(background)
            if not background_path.exists():
                logger.error(f"Background video file not found: {background}")
                return False
            if not background_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
                logger.error(f"Unsupported background video format: {background_path.suffix}")
                logger.error("Supported formats: .mp4, .avi, .mov, .mkv, .webm")
                return False
            logger.info(f"Using background video: {background}")
        else:
            logger.info("No background video provided - will generate single color background")

        logger.info(f"Generating {duration}s lo-fi video with {style} style...")

        try:
            # Determine if we need multiple tracks for long videos
            LONG_VIDEO_THRESHOLD = 600  # 10 minutes

            if duration > LONG_VIDEO_THRESHOLD:
                # Long video - use multiple tracks
                logger.info("🔍 Searching for multiple lo-fi tracks for long video...")
                tracks = await self.music_manager.get_tracks_for_long_video(duration, lofi_style)

                if not tracks:
                    logger.error(f"No suitable lo-fi tracks found for {style} style, {duration}s duration")
                    logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
                    return False

                logger.info(f"Found {len(tracks)} tracks")

                # Download all audio tracks
                audio_data_list = await self.music_manager.download_multiple_tracks_audio(tracks)

                if not audio_data_list:
                    logger.error("Failed to download any audio tracks")
                    return False

                logger.info(f"Downloaded {len(audio_data_list)} audio tracks")

                # Generate video from multiple tracks
                logger.info("🎬 Generating video from multiple tracks...")
                video_path = await self.video_generator.generate_video_from_multiple_tracks(
                    tracks, audio_data_list, duration, lofi_style, output, background
                )

                # Set up variables for attribution display
                used_tracks = tracks

            else:
                # Short video - use single track
                logger.info("🔍 Searching for lo-fi music...")
                track = await self.music_manager.get_track_for_video(duration, lofi_style)

                if not track:
                    logger.error(f"No suitable lo-fi tracks found for {style} style, {duration}s duration")
                    logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
                    return False

                logger.info(f"Found track: '{track.title}' by {track.artist}")
                logger.info(f"   Duration: {track.duration:.1f}s | License: {track.license_type.value}")
                logger.info(f"   Source: {track.source}")

                # Download audio
                logger.info("⬇️  Downloading audio...")
                audio_data = await self.music_manager.download_track_audio(track)

                if not audio_data:
                    logger.error(f"Failed to download audio for track {track.id}")
                    return False

                logger.info(f"Downloaded {len(audio_data)} bytes of audio data")

                # Generate video
                logger.info("🎬 Generating video...")
                video_path = await self.video_generator.generate_video(
                    track, audio_data, duration, lofi_style, output, background
                )

                # Set up variables for attribution display
                used_tracks = [track]

            if not video_path:
                logger.error("Video generation failed")
                return False

            logger.info(f"Video generated successfully: {video_path.name}")
            logger.info(f"Output: {video_path}")

            # Show attribution if required
            if len(used_tracks) == 1:
                attribution = self.music_manager.get_attribution_text(used_tracks[0])
                if attribution:
                    logger.info("\n📜 Attribution required:")
                    for attr in attribution:
                        logger.info(f"   {attr}")
                else:
                    logger.info("\n✅ No attribution required (public domain)")
            else:
                # Multiple tracks attribution
                attribution_list = self.music_manager.get_attribution_text_for_multiple(used_tracks)
                if attribution_list:
                    logger.info(f"\n📜 Attribution required for {len(used_tracks)} tracks:")
                    for i, attr in enumerate(attribution_list, 1):
                        logger.info(f"   {i}. {attr}")
                else:
                    logger.info(f"\n✅ No attribution required for {len(used_tracks)} tracks (public domain)")

            # Show video info
            video_info = self.video_generator.get_video_info(video_path)
            if video_info:
                format_info = video_info.get('format', {})
                file_size = int(format_info.get('size', 0))
                if file_size > 0:
                    logger.info(f"📊 File size: {file_size / (1024*1024):.1f} MB")

            # Log successful generation
            if len(used_tracks) == 1:
                track = used_tracks[0]
                logger.info(f"Video generated successfully: {video_path.name}")
                logger.info(f"Track: {track.title} by {track.artist}")
                logger.info(f"Duration: {duration}s")
                logger.info(f"Style: {style}")
                logger.info(f"Output: {video_path}")
            else:
                logger.info(f"Multi-track video generated successfully: {video_path.name}")
                logger.info(f"Duration: {duration}s")
                logger.info(f"Style: {style}")
                logger.info(f"Output: {video_path}")
            return True
        except Exception as e:
            logger.error(f"Error: {e}")
            return False

    async def list_styles(self):
        """List available lo-fi styles."""
        logger.info("🎨 Available Lo-Fi Styles:")
        logger.info("=" * 30)

        styles = LoFiMusicManager.get_available_styles()
        for style in styles:
            style_enum = LoFiMusicManager.parse_style(style)
            keywords = LoFiMusicManager.STYLE_KEYWORDS.get(style_enum, [])
            logger.info(f"  {style:<12} - {', '.join(keywords[:3])}")

    async def preview_style(self, style: str, count: int = 5):
        """Preview tracks available for a style."""
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            logger.error(f"Invalid style: {style}")
            return False

        logger.info(f"🎵 Preview of {style} lo-fi tracks:")
        logger.info("=" * 40)

        try:
            tracks = await self.music_manager.find_lofi_for_style(lofi_style, 180.0, limit=count)

            if not tracks:
                logger.error("No tracks found for this style")
                return False

            for i, track in enumerate(tracks, 1):
                logger.info(f"{i}. {track.title}")
                logger.info(f"   Artist: {track.artist}")
                logger.info(f"   Duration: {track.duration:.1f}s")
                logger.info(f"   Tags: {', '.join(track.tags[:5])}")

            return True

        except Exception as e:
            logger.error(f"Error: {e}")
            return False

    async def upload_video_file(self, video_path: str, style: str,
                               schedule_time: Optional[str] = None,
                               schedule_date: Optional[str] = None):
        """Upload a video file to YouTube."""
        from datetime import datetime

        video_file = Path(video_path)
        if not video_file.exists():
            logger.error(f"❌ Video file not found: {video_path}")
            return False

        # Parse style
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            logger.error(f"❌ Invalid style: {style}")
            logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
            return False

        # Parse scheduled publish time if provided
        scheduled_time = None
        if schedule_time:
            try:
                scheduled_time = self.upload_pipeline.scheduler.parse_time_string(
                    schedule_time, schedule_date
                )
                logger.info(f"📅 Video will be published at: {scheduled_time}")
            except Exception as e:
                logger.error(f"❌ Invalid schedule time: {e}")
                return False

        # For upload, we need to create dummy track data since we don't have the original tracks
        # This is a limitation - ideally we'd store track metadata with the video
        from src.music_sources.base import Track, LicenseType
        dummy_track = Track(
            id="unknown",
            title="Lo-Fi Track",
            artist="Unknown Artist",
            duration=0.0,  # Will be determined from video
            license_type=LicenseType.CC0,
            source="Local File"
        )

        # Get video duration (this is a simplified approach)
        # In a real implementation, you'd want to extract this from the video file
        import subprocess
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1', str(video_file)
            ], capture_output=True, text=True)
            duration = float(result.stdout.strip())
        except:
            duration = 120.0  # Default fallback

        logger.info(f"🚀 Uploading video: {video_file.name}")

        youtube_video_id = await self.upload_pipeline.upload_video(
            video_file, [dummy_track], lofi_style, duration, scheduled_time
        )

        if youtube_video_id:
            logger.info(f"✅ Upload successful! Video ID: {youtube_video_id}")
            logger.info(f"🔗 Video URL: https://www.youtube.com/watch?v={youtube_video_id}")
            return True
        else:
            logger.error("❌ Upload failed")
            return False

    async def publish_video(self, video_id: int):
        """Publish a video immediately."""
        logger.info(f"📢 Publishing video {video_id}...")

        success = await self.upload_pipeline.publish_video_now(video_id)

        if success:
            logger.info(f"✅ Video {video_id} published successfully")
            return True
        else:
            logger.error(f"❌ Failed to publish video {video_id}")
            return False

    async def upload_status(self):
        """Show upload pipeline status."""
        logger.info("📊 Getting upload pipeline status...")

        status = await self.upload_pipeline.get_upload_status()

        if "error" in status:
            logger.error(f"❌ Error getting status: {status['error']}")
            return False

        logger.info("📈 Upload Pipeline Status:")
        logger.info(f"  Database: {'✅' if status['database_initialized'] else '❌'}")
        logger.info(f"  YouTube: {'✅' if status['youtube_authenticated'] else '❌'}")
        logger.info(f"  Slack: {'✅' if status['slack_enabled'] else '❌'}")

        logger.info("\n📊 Videos by Status:")
        for status_name, count in status['videos_by_status'].items():
            logger.info(f"  {status_name}: {count}")

        if status['next_scheduled']:
            logger.info("\n📅 Next Scheduled Videos:")
            for video in status['next_scheduled']:
                scheduled_time = video['scheduled_time'].strftime("%Y-%m-%d %H:%M")
                logger.info(f"  {video['title']} - {scheduled_time}")

        return True

    async def cleanup(self):
        """Clean up resources."""
        if self.upload_pipeline:
            await self.upload_pipeline.cleanup()
        if self.music_manager:
            await self.music_manager.close()
        if self.video_generator:
            self.video_generator.cleanup_temp_files()


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Generate lo-fi music videos",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s generate 120 calming                              # 2-minute calming video
  %(prog)s generate 300 upbeat -o my_video.mp4               # 5-minute upbeat video
  %(prog)s generate 180 chill -b background.mp4              # 3-minute video with custom background
  %(prog)s list-styles                                       # Show available styles
  %(prog)s preview chill                                     # Preview chill tracks
  %(prog)s upload video.mp4 chill                            # Upload video as draft
  %(prog)s upload video.mp4 chill -t 22:00                   # Upload and schedule for 10 PM today
  %(prog)s upload video.mp4 chill -t 22:00 -d 2024-12-25     # Upload and schedule for specific date
  %(prog)s publish 5                                         # Publish video with database ID 5
  %(prog)s status                                            # Show upload pipeline status
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate a lo-fi video')
    gen_parser.add_argument('duration', type=float, help='Video duration in seconds')
    gen_parser.add_argument('style', help='Lo-fi style (e.g., calming, upbeat, chill)')
    gen_parser.add_argument('-o', '--output', help='Output filename')
    gen_parser.add_argument('-b', '--background', help='Background video file path (optional)')

    # List styles command
    subparsers.add_parser('list-styles', help='List available lo-fi styles')

    # Preview command
    preview_parser = subparsers.add_parser('preview', help='Preview tracks for a style')
    preview_parser.add_argument('style', help='Lo-fi style to preview')
    preview_parser.add_argument('-c', '--count', type=int, default=5, help='Number of tracks to show')

    # Upload command
    upload_parser = subparsers.add_parser('upload', help='Upload a video to YouTube')
    upload_parser.add_argument('video_path', help='Path to video file')
    upload_parser.add_argument('style', help='Lo-fi style of the video')
    upload_parser.add_argument('-t', '--time', help='Scheduled publish time (HH:MM)')
    upload_parser.add_argument('-d', '--date', help='Scheduled publish date (YYYY-MM-DD)')

    # Publish command
    publish_parser = subparsers.add_parser('publish', help='Publish a video immediately')
    publish_parser.add_argument('video_id', type=int, help='Database video ID to publish')

    # Status command
    subparsers.add_parser('status', help='Show upload pipeline status')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    async def run_cli():
        cli = LoFiCLI()

        try:
            if args.command == 'list-styles':
                await cli.list_styles()
                return

            # For upload commands, we need to setup with upload enabled
            enable_upload = args.command in ['upload', 'publish', 'status']
            if not await cli.setup(enable_upload=enable_upload):
                return

            if args.command == 'generate':
                success = await cli.generate_video(args.duration, args.style, args.output, args.background)
                if not success:
                    sys.exit(1)

            elif args.command == 'preview':
                success = await cli.preview_style(args.style, args.count)
                if not success:
                    sys.exit(1)

            elif args.command == 'upload':
                success = await cli.upload_video_file(args.video_path, args.style, args.time, args.date)
                if not success:
                    sys.exit(1)

            elif args.command == 'publish':
                success = await cli.publish_video(args.video_id)
                if not success:
                    sys.exit(1)

            elif args.command == 'status':
                success = await cli.upload_status()
                if not success:
                    sys.exit(1)

        finally:
            await cli.cleanup()

    # Run the async CLI
    try:
        asyncio.run(run_cli())
    except KeyboardInterrupt:
        logger.info("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
