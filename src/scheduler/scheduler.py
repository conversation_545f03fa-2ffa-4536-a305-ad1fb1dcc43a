"""
Video publishing scheduler for YouTube uploads using modern APScheduler.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, List
from zoneinfo import ZoneInfo

from apscheduler import AsyncScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger

# Always use absolute imports
from database.manager import DatabaseManager
from database.models import UploadStatus
from youtube.uploader import YouTubeUploader
from notifications.slack import SlackNotifier

logger = logging.getLogger(__name__)


class VideoScheduler:
    """Manages scheduled video publishing using modern APScheduler."""

    def __init__(self,
                 db_manager: DatabaseManager,
                 youtube_uploader: YouTubeUploader,
                 slack_notifier: Optional[SlackNotifier] = None,
                 timezone: str = "UTC"):
        """
        Initialize video scheduler.

        Args:
            db_manager: Database manager instance
            youtube_uploader: YouTube uploader instance
            slack_notifier: Slack notifier instance (optional)
            timezone: Timezone for scheduling (default: UTC)
        """
        self.db_manager = db_manager
        self.youtube_uploader = youtube_uploader
        self.slack_notifier = slack_notifier
        self.timezone = ZoneInfo(timezone)
        self.scheduler = AsyncScheduler(timezone=self.timezone)
        self._running = False
    
    async def schedule_video_publication(self, 
                                       video_id: int, 
                                       publish_time: datetime) -> bool:
        """
        Schedule a video for publication.
        
        Args:
            video_id: Database video ID
            publish_time: When to publish the video
            
        Returns:
            True if scheduled successfully, False otherwise
        """
        try:
            # Update video status to scheduled
            success = await self.db_manager.update_video_status(
                video_id, 
                UploadStatus.SCHEDULED
            )
            
            if success:
                # Update scheduled publish time
                await self.db_manager.initialize()
                import aiosqlite
                async with aiosqlite.connect(self.db_manager.db_path) as db:
                    await db.execute("""
                        UPDATE uploaded_videos
                        SET scheduled_publish_time = ?, updated_at = ?
                        WHERE id = ?
                    """, (
                        publish_time.isoformat(),
                        datetime.now().isoformat(),
                        video_id
                    ))
                    await db.commit()
                
                logger.info(f"✅ Video {video_id} scheduled for {publish_time}")
                
                # Notify via Slack
                if self.slack_notifier:
                    # Get video details for notification
                    videos = await self.db_manager.get_videos_by_status(UploadStatus.SCHEDULED)
                    video = next((v for v in videos if v.id == video_id), None)
                    if video and video.youtube_video_id:
                        await self.slack_notifier.notify_publishing_scheduled(
                            video.title, 
                            video.youtube_video_id, 
                            publish_time
                        )
                
                return True
            else:
                logger.error(f"❌ Failed to schedule video {video_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error scheduling video {video_id}: {e}")
            return False
    
    async def check_and_publish_scheduled_videos(self) -> int:
        """
        Check for videos scheduled for publication and publish them.
        
        Returns:
            Number of videos published
        """
        try:
            now = datetime.now(self.timezone)
            
            # Get videos scheduled for publication
            scheduled_videos = await self.db_manager.get_scheduled_videos(now)
            
            if not scheduled_videos:
                logger.debug("No videos scheduled for publication")
                return 0
            
            published_count = 0
            
            for video in scheduled_videos:
                if not video.youtube_video_id:
                    logger.warning(f"⚠️ Video {video.id} has no YouTube ID, skipping")
                    continue
                
                try:
                    # Update video status to public on YouTube
                    success = await self.youtube_uploader.update_video_status(
                        video.youtube_video_id,
                        privacy_status="public"
                    )
                    
                    if success:
                        # Mark as published in database
                        await self.db_manager.mark_published(video.id)
                        published_count += 1
                        
                        logger.info(f"✅ Published video: {video.title} ({video.youtube_video_id})")
                        
                        # Notify via Slack
                        if self.slack_notifier:
                            video_url = f"https://www.youtube.com/watch?v={video.youtube_video_id}"
                            await self.slack_notifier.notify_published(
                                video.title,
                                video.youtube_video_id,
                                video_url
                            )
                    else:
                        logger.error(f"❌ Failed to publish video {video.id} on YouTube")
                        
                        # Notify failure via Slack
                        if self.slack_notifier:
                            await self.slack_notifier.notify_publishing_failed(
                                video.title,
                                video.youtube_video_id,
                                "YouTube API error"
                            )
                
                except Exception as e:
                    logger.error(f"❌ Error publishing video {video.id}: {e}")
                    
                    # Notify failure via Slack
                    if self.slack_notifier:
                        await self.slack_notifier.notify_publishing_failed(
                            video.title,
                            video.youtube_video_id or "unknown",
                            str(e)
                        )
            
            if published_count > 0:
                logger.info(f"✅ Published {published_count} videos")
            
            return published_count
            
        except Exception as e:
            logger.error(f"❌ Error checking scheduled videos: {e}")
            return 0
    
    async def start_scheduler(self, check_interval: int = 300) -> None:
        """
        Start the modern APScheduler.

        Args:
            check_interval: How often to check for scheduled videos (seconds)
        """
        if self._running:
            logger.warning("⚠️ Scheduler is already running")
            return

        try:
            # Start the scheduler
            await self.scheduler.start_in_background()

            # Add recurring job to check for scheduled videos
            await self.scheduler.add_schedule(
                self.check_and_publish_scheduled_videos,
                IntervalTrigger(seconds=check_interval),
                id="check_scheduled_videos"
            )

            self._running = True
            logger.info(f"🚀 Started modern video scheduler (check interval: {check_interval}s)")

        except Exception as e:
            logger.error(f"❌ Failed to start scheduler: {e}")
            if self.slack_notifier:
                await self.slack_notifier.notify_system_error(
                    "Video Scheduler",
                    str(e),
                    {"check_interval": check_interval}
                )
            raise
    
    async def stop_scheduler(self) -> None:
        """Stop the modern scheduler."""
        if self._running:
            try:
                await self.scheduler.stop()
                self._running = False
                logger.info("🛑 Video scheduler stopped")
            except Exception as e:
                logger.error(f"❌ Error stopping scheduler: {e}")
        else:
            logger.warning("⚠️ Scheduler is not running")
    
    async def get_next_scheduled_videos(self, limit: int = 10) -> List[dict]:
        """
        Get upcoming scheduled videos.
        
        Args:
            limit: Maximum number of videos to return
            
        Returns:
            List of video information dictionaries
        """
        try:
            scheduled_videos = await self.db_manager.get_videos_by_status(UploadStatus.SCHEDULED)
            
            # Sort by scheduled time
            scheduled_videos.sort(
                key=lambda v: v.scheduled_publish_time or datetime.min,
                reverse=False
            )
            
            result = []
            for video in scheduled_videos[:limit]:
                if video.scheduled_publish_time:
                    result.append({
                        "id": video.id,
                        "title": video.title,
                        "youtube_video_id": video.youtube_video_id,
                        "scheduled_time": video.scheduled_publish_time,
                        "style": video.style,
                        "duration": video.duration
                    })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error getting scheduled videos: {e}")
            return []
    
    def parse_time_string(self, time_str: str, date_str: Optional[str] = None) -> datetime:
        """
        Parse time string into datetime object.
        
        Args:
            time_str: Time in format "HH:MM" or "HH:MM:SS"
            date_str: Date in format "YYYY-MM-DD" (default: today)
            
        Returns:
            Datetime object in the configured timezone
        """
        if date_str:
            date_part = datetime.strptime(date_str, "%Y-%m-%d").date()
        else:
            date_part = datetime.now(self.timezone).date()
        
        # Parse time
        if ":" in time_str:
            time_parts = time_str.split(":")
            if len(time_parts) == 2:
                hour, minute = map(int, time_parts)
                second = 0
            elif len(time_parts) == 3:
                hour, minute, second = map(int, time_parts)
            else:
                raise ValueError(f"Invalid time format: {time_str}")
        else:
            raise ValueError(f"Invalid time format: {time_str}")
        
        # Combine date and time
        naive_dt = datetime.combine(date_part, datetime.min.time().replace(
            hour=hour, minute=minute, second=second
        ))
        
        # Apply timezone
        return naive_dt.replace(tzinfo=self.timezone)
    
    def get_default_publish_time(self, days_ahead: int = 0) -> datetime:
        """
        Get default publish time (e.g., 22:00 today or specified days ahead).
        
        Args:
            days_ahead: Number of days from today
            
        Returns:
            Datetime object for default publish time
        """
        now = datetime.now(self.timezone)
        target_date = now.date() + timedelta(days=days_ahead)
        
        # Default to 22:00 (10 PM)
        default_time = datetime.combine(
            target_date, 
            datetime.min.time().replace(hour=22, minute=0)
        )
        
        return default_time.replace(tzinfo=self.timezone)
