"""
Modern CLI interface using Typer and Rich.
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Annotated

import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Always use absolute imports since we're adding src to path
from lofi_manager import LoFiMusicManager
from video_generator import LoFiVideoGenerator
from upload_pipeline import UploadPipeline
from error_tracking import initialize_error_tracking
from music_sources.base import LoFiStyle, Track, LicenseType

# Initialize Rich console
console = Console()

# Create Typer app
app = typer.Typer(
    name="lofi-channel",
    help="🎵 Modern Lo-Fi Video Generator with YouTube Upload Pipeline",
    rich_markup_mode="rich",
    no_args_is_help=True
)

# Global state
music_manager: Optional[LoFiMusicManager] = None
video_generator: Optional[LoFiVideoGenerator] = None
upload_pipeline: Optional[UploadPipeline] = None


async def setup_core_components() -> bool:
    """Setup core components (music manager and video generator)."""
    global music_manager, video_generator
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()

        # Initialize error tracking
        sentry_dsn = os.getenv("SENTRY_DSN")
        if sentry_dsn:
            initialize_error_tracking(
                dsn=sentry_dsn,
                environment=os.getenv("ENVIRONMENT", "production"),
                debug=os.getenv("DEBUG", "false").lower() == "true"
            )

        # Initialize music manager
        music_manager = LoFiMusicManager()

        # Configure music sources
        freesound_key = os.getenv("FREESOUND_API_KEY")
        if not freesound_key:
            console.print("❌ [red]FREESOUND_API_KEY not found in environment[/red]")
            console.print("Please set your Freesound API key:")
            console.print("export FREESOUND_API_KEY='your_key_here'")
            console.print("Get your key at: https://freesound.org/apiv2/apply")
            return False

        music_manager.configure_sources(freesound_key)

        # Initialize video generator
        video_generator = LoFiVideoGenerator()

        # Check video dependencies
        deps_ok, missing = video_generator.check_dependencies()
        if not deps_ok:
            console.print("❌ [red]Missing dependencies:[/red]")
            for dep in missing:
                console.print(f"  - {dep}")
            console.print("\n[yellow]Please install FFmpeg:[/yellow]")
            console.print("  Ubuntu/Debian: sudo apt install ffmpeg")
            console.print("  macOS: brew install ffmpeg")
            console.print("  Windows: Download from https://ffmpeg.org/")
            return False

        return True

    except Exception as e:
        console.print(f"❌ [red]Setup failed: {e}[/red]")
        return False


async def setup_upload_pipeline() -> bool:
    """Setup upload pipeline components."""
    global upload_pipeline
    
    try:
        service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
        slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        timezone = os.getenv("TIMEZONE", "UTC")
        
        if not service_account_file:
            console.print("❌ [red]YOUTUBE_SERVICE_ACCOUNT_FILE not found in environment[/red]")
            console.print("Please set your YouTube service account file path:")
            console.print("export YOUTUBE_SERVICE_ACCOUNT_FILE='path/to/service-account.json'")
            return False
        
        upload_pipeline = UploadPipeline(
            service_account_file=service_account_file,
            slack_webhook_url=slack_webhook_url,
            timezone=timezone
        )
        
        return await upload_pipeline.initialize()
        
    except Exception as e:
        console.print(f"❌ [red]Upload pipeline setup failed: {e}[/red]")
        return False


@app.command()
def generate(
    duration: Annotated[float, typer.Argument(help="Video duration in seconds")],
    style: Annotated[str, typer.Argument(help="Lo-fi style (e.g., calming, upbeat, chill)")],
    output: Annotated[Optional[str], typer.Option("-o", "--output", help="Output filename")] = None,
    background: Annotated[Optional[str], typer.Option("-b", "--background", help="Background video file path")] = None,
):
    """🎬 Generate a lo-fi music video."""
    
    async def _generate():
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Setup
            task = progress.add_task("Setting up components...", total=None)
            if not await setup_core_components():
                return False
            
            # Parse style
            progress.update(task, description="Parsing style...")
            lofi_style = LoFiMusicManager.parse_style(style)
            if not lofi_style:
                console.print(f"❌ [red]Invalid style: {style}[/red]")
                console.print(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
                return False
            
            # Generate video
            progress.update(task, description="Finding music...")
            
            if duration > 180:  # Long video - use multiple tracks
                tracks = await music_manager.find_multiple_tracks_for_duration(duration, lofi_style)
                if not tracks:
                    console.print("❌ [red]No suitable tracks found for long video[/red]")
                    return False
                
                progress.update(task, description="Downloading audio tracks...")
                audio_data_list = []
                for track in tracks:
                    audio_data = await music_manager.download_audio(track)
                    if audio_data:
                        audio_data_list.append(audio_data)
                
                if not audio_data_list:
                    console.print("❌ [red]Failed to download audio tracks[/red]")
                    return False
                
                progress.update(task, description="Generating video from multiple tracks...")
                video_path = await video_generator.generate_video_from_multiple_tracks(
                    tracks, audio_data_list, duration, lofi_style, output, background
                )
                used_tracks = tracks
            else:
                # Short video - use single track
                track = await music_manager.get_track_for_video(duration, lofi_style)
                if not track:
                    console.print(f"❌ [red]No suitable tracks found for {style} style[/red]")
                    return False
                
                progress.update(task, description="Downloading audio...")
                audio_data = await music_manager.download_audio(track)
                if not audio_data:
                    console.print("❌ [red]Failed to download audio[/red]")
                    return False
                
                progress.update(task, description="Generating video...")
                video_path = await video_generator.generate_video(
                    track, audio_data, duration, lofi_style, output, background
                )
                used_tracks = [track]
            
            if video_path:
                console.print(f"✅ [green]Video generated successfully![/green]")
                console.print(f"📁 Output: {video_path}")
                
                # Show attribution
                console.print("\n🎵 [bold]Track Attribution:[/bold]")
                for i, track in enumerate(used_tracks, 1):
                    console.print(f"  {i}. {track.title} by {track.artist} ({track.source})")
                
                return True
            else:
                console.print("❌ [red]Video generation failed[/red]")
                return False
    
    # Run async function
    success = asyncio.run(_generate())
    if not success:
        raise typer.Exit(1)


@app.command()
def list_styles():
    """📋 List available lo-fi styles."""
    
    console.print("\n🎵 [bold]Available Lo-Fi Styles:[/bold]")
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Style", style="cyan")
    table.add_column("Keywords", style="green")
    table.add_column("BPM Range", style="yellow")
    
    for style in LoFiStyle:
        keywords = LoFiMusicManager.STYLE_KEYWORDS.get(style, [])
        bpm_range = LoFiMusicManager.STYLE_BPM_RANGES.get(style, (0, 0))
        
        table.add_row(
            style.value,
            ", ".join(keywords[:3]) + ("..." if len(keywords) > 3 else ""),
            f"{bpm_range[0]}-{bpm_range[1]}"
        )
    
    console.print(table)


@app.command()
def preview(
    style: Annotated[str, typer.Argument(help="Lo-fi style to preview")],
    count: Annotated[int, typer.Option("-c", "--count", help="Number of tracks to show")] = 5,
):
    """🎧 Preview tracks available for a style."""
    
    async def _preview():
        if not await setup_core_components():
            return False
        
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            console.print(f"❌ [red]Invalid style: {style}[/red]")
            return False
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task(f"Finding {style} tracks...", total=None)
            
            tracks = await music_manager.find_lofi_for_style(lofi_style, 180.0, limit=count)
            
            if not tracks:
                console.print(f"❌ [red]No {style} tracks found[/red]")
                return False
            
            console.print(f"\n🎵 [bold]{style.title()} Lo-Fi Tracks Preview:[/bold]")
            
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("#", style="cyan", width=3)
            table.add_column("Title", style="green")
            table.add_column("Artist", style="yellow")
            table.add_column("Duration", style="blue")
            table.add_column("License", style="red")
            
            for i, track in enumerate(tracks, 1):
                duration_str = f"{int(track.duration // 60)}:{int(track.duration % 60):02d}"
                license_str = track.license_type.value if track.license_type else "Unknown"
                
                table.add_row(
                    str(i),
                    track.title[:40] + ("..." if len(track.title) > 40 else ""),
                    track.artist[:30] + ("..." if len(track.artist) > 30 else ""),
                    duration_str,
                    license_str
                )
            
            console.print(table)
            return True
    
    success = asyncio.run(_preview())
    if not success:
        raise typer.Exit(1)


@app.command()
def upload(
    video_path: Annotated[str, typer.Argument(help="Path to video file")],
    style: Annotated[str, typer.Argument(help="Lo-fi style of the video")],
    time: Annotated[Optional[str], typer.Option("-t", "--time", help="Scheduled publish time (HH:MM)")] = None,
    date: Annotated[Optional[str], typer.Option("-d", "--date", help="Scheduled publish date (YYYY-MM-DD)")] = None,
):
    """📤 Upload a video to YouTube."""

    async def _upload():
        # Setup upload pipeline
        if not await setup_upload_pipeline():
            return False

        video_file = Path(video_path)
        if not video_file.exists():
            console.print(f"❌ [red]Video file not found: {video_path}[/red]")
            return False

        # Parse style
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            console.print(f"❌ [red]Invalid style: {style}[/red]")
            return False

        # Parse scheduled publish time if provided
        scheduled_time = None
        if time:
            try:
                scheduled_time = upload_pipeline.scheduler.parse_time_string(time, date)
                console.print(f"📅 [yellow]Video will be published at: {scheduled_time}[/yellow]")
            except Exception as e:
                console.print(f"❌ [red]Invalid schedule time: {e}[/red]")
                return False

        # Create dummy track data (limitation of uploading existing videos)
        dummy_track = Track(
            id="unknown",
            title="Lo-Fi Track",
            artist="Unknown Artist",
            duration=0.0,
            license_type=LicenseType.CC0,
            source="Local File"
        )

        # Get video duration
        import subprocess
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1', str(video_file)
            ], capture_output=True, text=True)
            duration = float(result.stdout.strip())
        except:
            duration = 120.0  # Default fallback

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Uploading to YouTube...", total=None)

            youtube_video_id = await upload_pipeline.upload_video(
                video_file, [dummy_track], lofi_style, duration, scheduled_time
            )

            if youtube_video_id:
                console.print(f"✅ [green]Upload successful![/green]")
                console.print(f"🆔 Video ID: {youtube_video_id}")
                console.print(f"🔗 URL: https://www.youtube.com/watch?v={youtube_video_id}")
                if scheduled_time:
                    console.print(f"📅 Scheduled for: {scheduled_time}")
                return True
            else:
                console.print("❌ [red]Upload failed[/red]")
                return False

    success = asyncio.run(_upload())
    if not success:
        raise typer.Exit(1)


@app.command()
def publish(
    video_id: Annotated[int, typer.Argument(help="Database video ID to publish")],
):
    """📢 Publish a video immediately."""

    async def _publish():
        if not await setup_upload_pipeline():
            return False

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task(f"Publishing video {video_id}...", total=None)

            success = await upload_pipeline.publish_video_now(video_id)

            if success:
                console.print(f"✅ [green]Video {video_id} published successfully![/green]")
                return True
            else:
                console.print(f"❌ [red]Failed to publish video {video_id}[/red]")
                return False

    success = asyncio.run(_publish())
    if not success:
        raise typer.Exit(1)


@app.command()
def status():
    """📊 Show upload pipeline status."""

    async def _status():
        if not await setup_upload_pipeline():
            return False

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Getting pipeline status...", total=None)

            status_data = await upload_pipeline.get_upload_status()

            if "error" in status_data:
                console.print(f"❌ [red]Error getting status: {status_data['error']}[/red]")
                return False

            # Display status in a nice panel
            status_text = f"""
[bold green]Database:[/bold green] {'✅' if status_data['database_initialized'] else '❌'}
[bold blue]YouTube:[/bold blue] {'✅' if status_data['youtube_authenticated'] else '❌'}
[bold yellow]Slack:[/bold yellow] {'✅' if status_data['slack_enabled'] else '❌'}
            """

            console.print(Panel(status_text.strip(), title="📈 Pipeline Status", border_style="green"))

            # Videos by status table
            if status_data['videos_by_status']:
                console.print("\n📊 [bold]Videos by Status:[/bold]")

                table = Table(show_header=True, header_style="bold magenta")
                table.add_column("Status", style="cyan")
                table.add_column("Count", style="green")

                for status_name, count in status_data['videos_by_status'].items():
                    table.add_row(status_name.title(), str(count))

                console.print(table)

            # Next scheduled videos
            if status_data.get('next_scheduled'):
                console.print("\n📅 [bold]Next Scheduled Videos:[/bold]")

                table = Table(show_header=True, header_style="bold magenta")
                table.add_column("ID", style="cyan")
                table.add_column("Title", style="green")
                table.add_column("Scheduled Time", style="yellow")

                for video in status_data['next_scheduled']:
                    scheduled_time = video['scheduled_time'].strftime("%Y-%m-%d %H:%M")
                    table.add_row(
                        str(video['id']),
                        video['title'][:50] + ("..." if len(video['title']) > 50 else ""),
                        scheduled_time
                    )

                console.print(table)

            return True

    success = asyncio.run(_status())
    if not success:
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
