"""
Music source manager for coordinating multiple music sources.
"""

import asyncio
from typing import List, Dict, Optional, Any
from pathlib import Path
import json
import hashlib
import aiofiles

from music_sources.base import (
    MusicSource, Track, SearchQuery, SearchResult, LicenseType,
    MusicSourceError, get_attribution_text, is_commercial_use_allowed
)
from music_sources.freesound import FreesoundClient

import logging

logger = logging.getLogger(__name__)


class MusicCache:
    """Simple file-based cache for tracks and audio data."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.metadata_dir = self.cache_dir / "metadata"
        self.audio_dir = self.cache_dir / "audio"
        self.metadata_dir.mkdir(exist_ok=True)
        self.audio_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, source: str, track_id: str) -> str:
        """Generate cache key for a track."""
        return hashlib.md5(f"{source}:{track_id}".encode()).hexdigest()
    
    async def get_track_metadata(self, source: str, track_id: str) -> Optional[Track]:
        """Get cached track metadata."""
        cache_key = self._get_cache_key(source, track_id)
        metadata_file = self.metadata_dir / f"{cache_key}.json"
        
        if not metadata_file.exists():
            return None
        
        try:
            async with aiofiles.open(metadata_file, 'r') as f:
                data = json.loads(await f.read())
                # Reconstruct Track object from cached data
                return Track(**data)
        except Exception:
            return None
    
    async def cache_track_metadata(self, track: Track):
        """Cache track metadata."""
        cache_key = self._get_cache_key(track.source, track.id)
        metadata_file = self.metadata_dir / f"{cache_key}.json"
        
        try:
            # Convert Track to dict for JSON serialization
            track_dict = {
                'id': track.id,
                'title': track.title,
                'artist': track.artist,
                'duration': track.duration,
                'preview_url': track.preview_url,
                'download_url': track.download_url,
                'license_type': track.license_type.value,
                'attribution_text': track.attribution_text,
                'tags': track.tags,
                'source': track.source,
                'bpm': track.bpm,
                'genre': track.genre,
                'mood': track.mood,
                'file_size': track.file_size,
                'file_format': track.file_format
            }
            
            async with aiofiles.open(metadata_file, 'w') as f:
                await f.write(json.dumps(track_dict, indent=2))
        except Exception as e:
            logger.error(f"Failed to cache track metadata: {e}")
    
    async def get_audio_data(self, source: str, track_id: str, audio_type: str = "preview") -> Optional[bytes]:
        """Get cached audio data."""
        cache_key = self._get_cache_key(source, track_id)
        audio_file = self.audio_dir / f"{cache_key}_{audio_type}.mp3"
        
        if not audio_file.exists():
            return None
        
        try:
            async with aiofiles.open(audio_file, 'rb') as f:
                return await f.read()
        except Exception:
            return None
    
    async def cache_audio_data(self, source: str, track_id: str, audio_data: bytes, audio_type: str = "preview"):
        """Cache audio data."""
        cache_key = self._get_cache_key(source, track_id)
        audio_file = self.audio_dir / f"{cache_key}_{audio_type}.mp3"
        
        try:
            async with aiofiles.open(audio_file, 'wb') as f:
                await f.write(audio_data)
        except Exception as e:
            logger.error(f"Failed to cache audio data: {e}")


class MusicSourceManager:
    """Manages multiple music sources and provides unified interface."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.sources: Dict[str, MusicSource] = {}
        self.cache = MusicCache(cache_dir)
        self._default_search_params = SearchQuery(
            tags=['lofi', 'chill', 'ambient'],
            license_types=[LicenseType.CC0, LicenseType.CC_BY],
            min_duration=30.0,  # At least 30 seconds
            max_duration=600.0,  # At most 10 minutes
            limit=20
        )
    
    def add_source(self, source: MusicSource):
        """Add a music source to the manager."""
        self.sources[source.source_name] = source
    
    def configure_freesound(self, api_key: str):
        """Configure Freesound as a music source."""
        freesound = FreesoundClient(api_key)
        self.add_source(freesound)
    
    async def search_all_sources(self, query: SearchQuery = None) -> Dict[str, SearchResult]:
        """Search all configured sources."""
        if query is None:
            query = self._default_search_params
        
        results = {}
        tasks = []
        
        for source_name, source in self.sources.items():
            task = asyncio.create_task(self._search_source_safe(source, query))
            tasks.append((source_name, task))
        
        for source_name, task in tasks:
            try:
                result = await task
                results[source_name] = result
            except Exception as e:
                logger.error(f"Search failed for {source_name}: {e}")
                results[source_name] = SearchResult(tracks=[], total_count=0, has_more=False)
        
        return results
    
    async def _search_source_safe(self, source: MusicSource, query: SearchQuery) -> SearchResult:
        """Search a source with error handling."""
        try:
            return await source.search(query)
        except Exception as e:
            logger.error(f"Error searching {source.source_name}: {e}")
            return SearchResult(tracks=[], total_count=0, has_more=False)
    
    async def get_track_with_cache(self, source_name: str, track_id: str) -> Optional[Track]:
        """Get track with caching."""
        # Try cache first
        cached_track = await self.cache.get_track_metadata(source_name, track_id)
        if cached_track:
            return cached_track
        
        # Fetch from source
        source = self.sources.get(source_name)
        if not source:
            return None
        
        try:
            track = await source.get_track(track_id)
            if track:
                await self.cache.cache_track_metadata(track)
            return track
        except Exception as e:
            logger.error(f"Error fetching track {track_id} from {source_name}: {e}")
            return None
    
    async def get_audio_preview(self, track: Track) -> Optional[bytes]:
        """Get audio preview with caching."""
        # Try cache first
        cached_audio = await self.cache.get_audio_data(track.source, track.id, "preview")
        if cached_audio:
            return cached_audio
        
        # Fetch from source
        source = self.sources.get(track.source)
        if not source:
            return None
        
        try:
            audio_data = await source.download_preview(track)
            if audio_data:
                await self.cache.cache_audio_data(track.source, track.id, audio_data, "preview")
            return audio_data
        except Exception as e:
            logger.error(f"Error downloading preview for track {track.id}: {e}")
            return None
    
    def get_commercial_tracks(self, tracks: List[Track]) -> List[Track]:
        """Filter tracks that can be used commercially."""
        return [track for track in tracks if is_commercial_use_allowed(track)]
    
    def get_attribution_requirements(self, tracks: List[Track]) -> List[str]:
        """Get attribution requirements for a list of tracks."""
        attributions = []
        for track in tracks:
            attribution = get_attribution_text(track)
            if attribution:
                attributions.append(attribution)
        return attributions
    
    async def discover_lofi_tracks(self, limit: int = 50) -> List[Track]:
        """Discover lo-fi tracks from all sources."""
        query = SearchQuery(
            query="lofi chill ambient",
            tags=['lofi', 'chill', 'ambient', 'beats'],
            license_types=[LicenseType.CC0, LicenseType.CC_BY],
            min_duration=60.0,  # At least 1 minute
            max_duration=300.0,  # At most 5 minutes
            limit=limit
        )
        
        all_results = await self.search_all_sources(query)
        
        # Combine and deduplicate tracks
        all_tracks = []
        seen_tracks = set()
        
        for source_name, result in all_results.items():
            for track in result.tracks:
                # Simple deduplication based on title and artist
                track_key = f"{track.title.lower()}:{track.artist.lower()}"
                if track_key not in seen_tracks:
                    seen_tracks.add(track_key)
                    all_tracks.append(track)
        
        # Sort by some criteria (e.g., duration, source preference)
        all_tracks.sort(key=lambda t: (t.source == "Freesound", -t.duration))
        
        return all_tracks[:limit]
    
    async def close(self):
        """Close all sources."""
        for source in self.sources.values():
            await source.close()
